{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cb73fd16._.js", "server/edge/chunks/node_modules_@auth_core_b5890d95._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_74ea7e62._.js", "server/edge/chunks/[root-of-the-server]__b683c460._.js", "server/edge/chunks/edge-wrapper_551d8963.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dVF7qFKM2cQ8/z46hw4X7IjD66wh96fiTI69TXHPOYA=", "__NEXT_PREVIEW_MODE_ID": "457e92f036c643f89850de085e0eb85b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "521ac14465b5165540e4feae27581940c677733c2419da3fd5104738e687b364", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "55cf57701cd6d527c6ea7444fb3a144c6132cd97c581afecac371149316a1f7a"}}}, "instrumentation": null, "functions": {}}