{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/hooks/useAuthUser.ts"], "sourcesContent": ["// hooks/useAuthUser.ts\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function useAuthUser() {\r\n  const [user, setUser] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthenticated, setUnauthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    async function fetchUser() {\r\n      try {\r\n        const res = await fetch(\"/api/user\");\r\n        if (res.status === 401) {\r\n          setUnauthenticated(true);\r\n          setUser(null);\r\n        } else {\r\n          const data = await res.json();\r\n          setUser(data.user);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Failed to fetch user\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n  return { user, loading, unauthenticated };\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB;;AAEO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,MAAM,MAAM,MAAM,MAAM;gBACxB,IAAI,IAAI,MAAM,KAAK,KAAK;oBACtB,mBAAmB;oBACnB,QAAQ;gBACV,OAAO;oBACL,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,QAAQ,KAAK,IAAI;gBACnB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;QAAE;QAAM;QAAS;IAAgB;AAC1C", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/InputSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  <PERSON><PERSON>les,\r\n  Loader,\r\n  FileUp,\r\n  Terminal,\r\n  Monitor as MonitorIcon,\r\n  Figma,\r\n} from \"lucide-react\";\r\n\r\nconst EXAMPLE_ACTIONS = [\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Bayesian Theorem in ML\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Hidden Markov Models (HMM)\" },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Gaussian Mixture Models\",\r\n  },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Linked Lists in DSA\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Binary Trees in DSA\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Quadratic Equations in Maths\" },\r\n  {\r\n    icon: <FileUp className=\"h-4 w-4\" />,\r\n    text: \"Projectile Motion in Physics\",\r\n  },\r\n  {\r\n    icon: <MonitorIcon className=\"h-4 w-4\" />,\r\n    text: \"Dynamic Programming in DSA\",\r\n  },\r\n  {\r\n    icon: <Terminal className=\"h-4 w-4\" />,\r\n    text: \"Eigenvalues and Eigenvectors\",\r\n  },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Fourier Transform in Maths\" },\r\n  { icon: <Figma className=\"h-4 w-4\" />, text: \"Convex Optimization in Maths\" },\r\n  { icon: <MonitorIcon className=\"h-4 w-4\" />, text: \"Graph Theory in DSA\" },\r\n  { icon: <Terminal className=\"h-4 w-4\" />, text: \"Quantum Mechanics Basics\" },\r\n  { icon: <FileUp className=\"h-4 w-4\" />, text: \"Neural Networks in ML\" },\r\n];\r\n\r\ninterface InputSectionProps {\r\n  isGenerating: boolean;\r\n  onGenerate: (prompt: string) => void;\r\n}\r\n\r\nexport default function InputSection({\r\n  isGenerating,\r\n  onGenerate,\r\n}: InputSectionProps) {\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n  const [originalPrompt, setOriginalPrompt] = useState<string | null>(null);\r\n\r\n  const enhancePrompt = async (prompt: string) => {\r\n    if (!prompt.trim()) {\r\n      setInputValue(\"\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsEnhancing(true);\r\n      setOriginalPrompt(prompt.trim()); // Store the original prompt before enhancement\r\n      localStorage.setItem(\"currentPrompt\", prompt.trim());\r\n\r\n      const response = await fetch(\"/api/enhance\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt: prompt.trim() }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const enhancedPrompt = await response.text();\r\n      setInputValue(enhancedPrompt);\r\n    } catch (error) {\r\n      console.error(\"Error enhancing prompt:\", error);\r\n    } finally {\r\n      setIsEnhancing(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateClick = () => {\r\n    if (!inputValue.trim()) return;\r\n    // Use the original prompt if it exists, otherwise use the current input\r\n    const promptToUse = originalPrompt ?? inputValue.trim();\r\n    localStorage.setItem(\"currentPrompt\", promptToUse);\r\n    onGenerate(promptToUse);\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {!isGenerating && (\r\n        <motion.div\r\n          key=\"idle-ui\"\r\n          className=\"flex flex-grow flex-col h-full my-20 w-full items-center justify-center relative\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n        >\r\n          <div className=\"flex w-1/2 h-24 rounded-full bg-primary/20 blur-3xl absolute -top-10 left-1/2 -translate-x-1/2 text-foreground overflow-hidden\" />\r\n          <div className=\"mx-4 flex flex-col items-center\">\r\n            <div className=\"mb-12 text-center\">\r\n              <h1 className=\"mb-6 text-5xl md:text-6xl font-medium tracking-tight text-transparent bg-clip-text bg-gradient-to-br from-foreground to-muted/70 via-foreground/80\">\r\n                What do you want to learn?\r\n              </h1>\r\n              <p className=\"text-lg text-muted-foreground max-w-md mx-auto\">\r\n                Create animated explanations for any complex topic in minutes.\r\n                For both{\" \"}\r\n                <span className=\"font-medium text-foreground\">students</span>{\" \"}\r\n                and{\" \"}\r\n                <span className=\"font-medium text-foreground\">teachers</span> .\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mb-6 w-full max-w-xl\">\r\n              <div className=\"shadow-xl dark:shadow-primary/20 dark:shadow-2xl relative rounded-lg\">\r\n                <div className=\"flex flex-col rounded-lg border bg-gradient-to-b from-secondary/40 to-background p-3 pb-6 relative overflow-hidden\">\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none blur-2xl\"></div>\r\n                  <textarea\r\n                    placeholder=\"Explain bayes theorem in machine learning\"\r\n                    className=\"h-32 w-full outline-none resize-none text-sm\"\r\n                    value={inputValue}\r\n                    onChange={(e) => setInputValue(e.target.value)}\r\n                  />\r\n                  <div className=\"mt-auto flex gap-2 absolute bottom-2 right-2 \">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className={cn(\r\n                        \"backdrop-blur-lg shadow\",\r\n                        isEnhancing ? \"animate-pulse\" : null\r\n                      )}\r\n                      disabled={\r\n                        !inputValue.trim() ||\r\n                        isEnhancing ||\r\n                        inputValue.length < 6 ||\r\n                        inputValue.length > 300\r\n                      }\r\n                      onClick={() => {\r\n                        if (!inputValue.trim()) return;\r\n                        enhancePrompt(inputValue.trim());\r\n                      }}\r\n                    >\r\n                      {isEnhancing ? (\r\n                        <Loader className=\"animate-spin size-4\" />\r\n                      ) : (\r\n                        <Sparkles className=\"size-4\" />\r\n                      )}\r\n                      {isEnhancing ? \"Enhancing...\" : \"Enhance\"}\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={handleGenerateClick}\r\n                      disabled={!inputValue.trim() || isEnhancing}\r\n                    >\r\n                      Generate\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mx-auto mt-16 flex w-full max-w-6xl flex-wrap justify-center gap-2\">\r\n              {EXAMPLE_ACTIONS.map((action, index) => (\r\n                <Button\r\n                  key={index}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  className=\"rounded-full px-4 py-0.5 text-xs\"\r\n                  onClick={() => setInputValue(action.text)}\r\n                >\r\n                  {action.icon}\r\n                  <span>{action.text}</span>\r\n                </Button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAeA,MAAM,kBAAkB;IACtB;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAAyB;IACtE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QAAE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACtE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACpE;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAC7B,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,MAAM;IACR;IACA;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAA6B;IAC3E;QAAE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,MAAM;IAA+B;IAC5E;QAAE,oBAAM,8OAAC,wMAAA,CAAA,UAAW;YAAC,WAAU;;;;;;QAAc,MAAM;IAAsB;IACzE;QAAE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAAc,MAAM;IAA2B;IAC3E;QAAE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,MAAM;IAAwB;CACvE;AAOc,SAAS,aAAa,EACnC,YAAY,EACZ,UAAU,EACQ;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,cAAc;YACd;QACF;QAEA,IAAI;YACF,eAAe;YACf,kBAAkB,OAAO,IAAI,KAAK,+CAA+C;YACjF,aAAa,OAAO,CAAC,iBAAiB,OAAO,IAAI;YAEjD,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ,OAAO,IAAI;gBAAG;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,iBAAiB,MAAM,SAAS,IAAI;YAC1C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;QACxB,wEAAwE;QACxE,MAAM,cAAc,kBAAkB,WAAW,IAAI;QACrD,aAAa,OAAO,CAAC,iBAAiB;QACtC,WAAW;IACb;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,CAAC,8BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqJ;;;;;;8CAGnK,8OAAC;oCAAE,WAAU;;wCAAiD;wCAEnD;sDACT,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAgB;wCAAI;wCAC9D;sDACJ,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAe;;;;;;;;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CACC,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2BACA,cAAc,kBAAkB;oDAElC,UACE,CAAC,WAAW,IAAI,MAChB,eACA,WAAW,MAAM,GAAG,KACpB,WAAW,MAAM,GAAG;oDAEtB,SAAS;wDACP,IAAI,CAAC,WAAW,IAAI,IAAI;wDACxB,cAAc,WAAW,IAAI;oDAC/B;;wDAEC,4BACC,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAErB,cAAc,iBAAiB;;;;;;;8DAElC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,WAAW,IAAI,MAAM;8DACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,kIAAA,CAAA,SAAM;oCAEL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,cAAc,OAAO,IAAI;;wCAEvC,OAAO,IAAI;sDACZ,8OAAC;sDAAM,OAAO,IAAI;;;;;;;mCAPb;;;;;;;;;;;;;;;;;WA1ET;;;;;;;;;;AA0Fd", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/TaskProgressSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { Accordion } from \"@/components/ui/accordion\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { CheckCircle2, Clock, AlertCircle, Loader } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface Task {\r\n  id: string;\r\n  name: string;\r\n  status: \"pending\" | \"in-progress\" | \"completed\" | \"failed\";\r\n}\r\n\r\ninterface TaskProgressSidebarProps {\r\n  isGenerating: boolean;\r\n  tasks: Task[];\r\n  currentGeneratingScene?: string | null;\r\n}\r\n\r\nexport default function TaskProgressSidebar({\r\n  isGenerating = true,\r\n  tasks = [\r\n    { id: \"1\", name: \"Download assets\", status: \"completed\" },\r\n    { id: \"2\", name: \"Generate script\", status: \"completed\" },\r\n    { id: \"3\", name: \"Render video\", status: \"in-progress\" },\r\n    { id: \"4\", name: \"Upload to cloud\", status: \"pending\" },\r\n    { id: \"5\", name: \"Notify user\", status: \"pending\" },\r\n  ],\r\n  currentGeneratingScene = \"Scene 2: Rendering animation\",\r\n}: TaskProgressSidebarProps) {\r\n  const getStatusIcon = (status: Task[\"status\"]) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n      case \"in-progress\":\r\n        return <Loader className=\"h-4 w-4 text-cyan-500 animate-spin\" />;\r\n      case \"pending\":\r\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n      case \"failed\":\r\n        return <AlertCircle className=\"h-4 w-4 text-destructive\" />;\r\n      default:\r\n        return <Clock className=\"h-4 w-4 text-muted-foreground\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: Task[\"status\"]) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return \"bg-gradient-to-r via-green-500/10 border-green-500/20\";\r\n      case \"in-progress\":\r\n        return \"bg-gradient-to-r via-cyan-400/20 border-cyan-500/20\";\r\n      case \"pending\":\r\n        return \"bg-gradient-to-r via-yellow-500/10 border-yellow-500/20\";\r\n      case \"failed\":\r\n        return \"bg-gradient-to-r via-rose-500/10 border-rose-500/20\";\r\n      default:\r\n        return \"bg-secondary/30\";\r\n    }\r\n  };\r\n\r\n  if (!isGenerating) return null;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"col-span-1 lg:col-span-2 h-full flex flex-col border-dashed bg-background\"\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        exit={{ opacity: 0, x: -20 }}\r\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n      >\r\n        <div className=\"px-6 py-4 border-b border-dashed bg-background\">\r\n          <h3 className=\"text-lg\">Task Progress</h3>\r\n        </div>\r\n\r\n        <div className=\"flex-1 px-6 pt-4 pb-10\">\r\n          <div className=\"space-y-6\">\r\n            {/* Current Tasks */}\r\n            <div className=\"w-full\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-3\"></div>\r\n                <h4 className=\"text-sm font-semibold text-muted-foreground uppercase tracking-wide\">\r\n                  Current Progress\r\n                </h4>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                {tasks.map((task, index) => (\r\n                  <motion.div\r\n                    key={task.id}\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: index * 0.1, duration: 0.3 }}\r\n                    className={cn(\r\n                      \"relative overflow- w-full rounded-lg border cursor-pointer hover:saturate-150 hover:brightness-150 transition-all duration-300\",\r\n                      getStatusColor(task.status),\r\n                      task.status === \"in-progress\" && \"animate-bg-pan\"\r\n                    )}\r\n                  >\r\n                    <div className=\"flex items-center space-x-3 relative p-4 pr-2\">\r\n                      <div\r\n                        className={`p-2 rounded-lg transition-all duration-200 ${\r\n                          task.status === \"in-progress\"\r\n                            ? \"bg-primary/10 inset-shadow-xs inset-shadow-primary/30\"\r\n                            : task.status === \"completed\"\r\n                            ? \"bg-green-500/10 inset-shadow-xs inset-shadow-green-600/30\"\r\n                            : task.status === \"failed\"\r\n                            ? \"bg-rose-500/20\"\r\n                            : \"bg-yellow-500/20\"\r\n                        }`}\r\n                      >\r\n                        {getStatusIcon(task.status)}\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <h4 className=\"font-medium text-xs\">{task.name}</h4>\r\n                        {task.status === \"in-progress\" &&\r\n                          currentGeneratingScene && (\r\n                            <motion.p\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"text-xs text-muted-foreground mt-1 truncate\"\r\n                            >\r\n                              {currentGeneratingScene}\r\n                            </motion.p>\r\n                          )}\r\n                        {task.status === \"in-progress\" && (\r\n                          <div className=\"mt-2 w-full bg-secondary rounded-full h-1.5 relative\">\r\n                            <div className=\"bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4\"></div>\r\n                            <div className=\"bg-gradient-to-r from-teal-300 via-blue-500 to-violet-400 h-1.5 rounded-full animate-pulse w-3/4 absolute top-0 left-0 blur animate-pulse translate-y-0.5\"></div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div\r\n                        className={`text-[10px] font-medium px-2.5 py-1 rounded-full transition-all duration-200 ${\r\n                          task.status === \"completed\"\r\n                            ? \"bg-green-500/10 text-green-500 border border-green-500/30\"\r\n                            : task.status === \"in-progress\"\r\n                            ? \"bg-cyan-500/10 text-cyan-500 border border-cyan-500/30\"\r\n                            : task.status === \"failed\"\r\n                            ? \"bg-rose-500/10 text-rose-500 border border-rose-500/30\"\r\n                            : \"bg-yellow-500/10 text-yellow-500 border border-yellow-500/30\"\r\n                        }`}\r\n                      >\r\n                        {task.status}\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Progress Summary */}\r\n            <div className=\"p-4 bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-xl border border-border/30\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <span className=\"text-sm font-medium text-muted-foreground\">\r\n                  Overall Progress\r\n                </span>\r\n                <span className=\"text-sm font-bold\">\r\n                  {tasks.filter((t) => t.status === \"completed\").length}/\r\n                  {tasks.length}\r\n                </span>\r\n              </div>\r\n              <div className=\"w-full rounded-full h-2 relative\">\r\n                <div\r\n                  className=\"bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400  h-2 rounded-full transition-all duration-500\"\r\n                  style={{\r\n                    width: `${\r\n                      (tasks.filter((t) => t.status === \"completed\").length /\r\n                        tasks.length) *\r\n                      100\r\n                    }%`,\r\n                  }}\r\n                ></div>\r\n                <div\r\n                  className={cn(\r\n                    \"bg-gradient-to-r from-purple-500 to-violet-500 via-fuchsia-400 h-2 rounded-full transition-all duration-500 absolute top-0 left-0 blur animate-pulse translate-y-0.5\"\r\n                  )}\r\n                  style={{\r\n                    width: `${\r\n                      (tasks.filter((t) => t.status === \"completed\").length /\r\n                        tasks.length) *\r\n                      100\r\n                    }%`,\r\n                  }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAGA;AAAA;AAAA;AAAA;AACA;AANA;;;;;AAoBe,SAAS,oBAAoB,EAC1C,eAAe,IAAI,EACnB,QAAQ;IACN;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAY;IACxD;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAY;IACxD;QAAE,IAAI;QAAK,MAAM;QAAgB,QAAQ;IAAc;IACvD;QAAE,IAAI;QAAK,MAAM;QAAmB,QAAQ;IAAU;IACtD;QAAE,IAAI;QAAK,MAAM;QAAe,QAAQ;IAAU;CACnD,EACD,yBAAyB,8BAA8B,EAC9B;IACzB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;;8BAE7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAU;;;;;;;;;;;8BAG1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAG,WAAU;0DAAsE;;;;;;;;;;;;kDAItF,8OAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kIACA,eAAe,KAAK,MAAM,GAC1B,KAAK,MAAM,KAAK,iBAAiB;0DAGnC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,MAAM,KAAK,gBACZ,0DACA,KAAK,MAAM,KAAK,cAChB,8DACA,KAAK,MAAM,KAAK,WAChB,mBACA,oBACJ;sEAED,cAAc,KAAK,MAAM;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAuB,KAAK,IAAI;;;;;;gEAC7C,KAAK,MAAM,KAAK,iBACf,wCACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,SAAS;wEAAE,SAAS;oEAAE;oEACtB,SAAS;wEAAE,SAAS;oEAAE;oEACtB,WAAU;8EAET;;;;;;gEAGN,KAAK,MAAM,KAAK,+BACf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;sEAIrB,8OAAC;4DACC,WAAW,CAAC,6EAA6E,EACvF,KAAK,MAAM,KAAK,cACZ,8DACA,KAAK,MAAM,KAAK,gBAChB,2DACA,KAAK,MAAM,KAAK,WAChB,2DACA,gEACJ;sEAED,KAAK,MAAM;;;;;;;;;;;;+CAtDX,KAAK,EAAE;;;;;;;;;;;;;;;;0CA+DpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;0DAG5D,8OAAC;gDAAK,WAAU;;oDACb,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM;oDAAC;oDACrD,MAAM,MAAM;;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,GACL,AAAC,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM,GACnD,MAAM,MAAM,GACd,IACD,CAAC,CAAC;gDACL;;;;;;0DAEF,8OAAC;gDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;gDAEF,OAAO;oDACL,OAAO,GACL,AAAC,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM,GACnD,MAAM,MAAM,GACd,IACD,CAAC,CAAC;gDACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n        destructive:\r\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\r\n        outline: 'text-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Accordion({\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\r\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\r\n}\r\n\r\nfunction AccordionItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\r\n  return (\r\n    <AccordionPrimitive.Item\r\n      data-slot=\"accordion-item\"\r\n      className={cn(\"border-b last:border-b-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AccordionTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\r\n  return (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        data-slot=\"accordion-trigger\"\r\n        className={cn(\r\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  )\r\n}\r\n\r\nfunction AccordionContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\r\n  return (\r\n    <AccordionPrimitive.Content\r\n      data-slot=\"accordion-content\"\r\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\r\n      {...props}\r\n    >\r\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  )\r\n}\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,8OAAC,qKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,8OAAC,qKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_7e1f0032.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_7e1f0032-module__HozVda__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_7e1f0032.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22ScrollingtextAnimation.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/shared/ScrollingtextAnimation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Geist_Mono } from \"next/font/google\";\r\n\r\nconst geistMono = Geist_Mono({ subsets: [\"latin\"] });\r\n\r\ninterface ScrollingTextAnimationProps {\r\n  text: string;\r\n  wordsPerLine?: number;\r\n  linesVisible?: number;\r\n  animationSpeed?: number;\r\n  className?: string;\r\n  onComplete?: () => void;\r\n}\r\n\r\nexport function ScrollingTextAnimation({\r\n  text,\r\n  wordsPerLine = 6,\r\n  linesVisible = 5,\r\n  animationSpeed = 2000,\r\n  className,\r\n  onComplete,\r\n}: ScrollingTextAnimationProps) {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  const breakIntoLines = (text: string, wordsPerLine: number): string[] => {\r\n    const words = text.split(\" \").filter((word) => word.length > 0);\r\n    const lines: string[] = [];\r\n\r\n    for (let i = 0; i < words.length; i += wordsPerLine) {\r\n      const line = words.slice(i, i + wordsPerLine).join(\" \");\r\n      lines.push(line);\r\n    }\r\n\r\n    return lines;\r\n  };\r\n\r\n  const lines = breakIntoLines(text, wordsPerLine);\r\n  const centerIndex = Math.floor(linesVisible / 2);\r\n\r\n  // Auto-advance animation\r\n  useEffect(() => {\r\n    if (currentIndex >= lines.length - centerIndex - 1) {\r\n      if (currentIndex >= lines.length - centerIndex - 1 && onComplete) {\r\n        onComplete();\r\n      }\r\n      return;\r\n    }\r\n\r\n    const timer = setTimeout(() => {\r\n      setCurrentIndex((prev) => prev + 1);\r\n    }, animationSpeed);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [currentIndex, lines.length, centerIndex, animationSpeed, onComplete]);\r\n\r\n  const getVisibleLines = () => {\r\n    const startIndex = Math.max(0, currentIndex - centerIndex);\r\n    const endIndex = Math.min(lines.length, startIndex + linesVisible);\r\n\r\n    return lines.slice(startIndex, endIndex).map((line, index) => ({\r\n      text: line,\r\n      originalIndex: startIndex + index,\r\n      relativeIndex: index,\r\n    }));\r\n  };\r\n\r\n  const visibleLines = getVisibleLines();\r\n\r\n  const getLineStyle = (relativeIndex: number) => {\r\n    const distanceFromCenter = Math.abs(relativeIndex - centerIndex);\r\n\r\n    if (relativeIndex === centerIndex) {\r\n      // Center line - fully focused\r\n      return {\r\n        opacity: 1,\r\n        filter: \"blur(0px)\",\r\n        scale: 1,\r\n        y: 0,\r\n      };\r\n    } else {\r\n      const opacity = Math.max(0.3, 1 - distanceFromCenter * 0.3);\r\n      const blur = distanceFromCenter * 2;\r\n      const scale = Math.max(0.9, 1 - distanceFromCenter * 0.05);\r\n\r\n      return {\r\n        opacity,\r\n        filter: `blur(${blur}px)`,\r\n        scale,\r\n        y: 0,\r\n      };\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative h-[600px] bg-transparent flex flex-col justify-center items-center overflow-hidden\",\r\n        className\r\n      )}\r\n    >\r\n      <div className=\"relative w-full max-w-xl\">\r\n        <AnimatePresence mode=\"popLayout\">\r\n          {visibleLines.map((line, index) => (\r\n            <motion.div\r\n              key={`${line.originalIndex}-${line.text.substring(0, 10)}`}\r\n              className={cn(\r\n                \"absolute w-full text-start font-medium leading-tight tracking-tight\",\r\n                geistMono.className\r\n              )}\r\n              initial={{\r\n                opacity: 0,\r\n                y: 50,\r\n                filter: \"blur(10px)\",\r\n                scale: 0.9,\r\n              }}\r\n              animate={{\r\n                ...getLineStyle(index),\r\n                y: (index - centerIndex) * 80,\r\n              }}\r\n              exit={{\r\n                opacity: 0,\r\n                y: -50,\r\n                filter: \"blur(10px)\",\r\n                scale: 0.9,\r\n              }}\r\n              transition={{\r\n                duration: 0.8,\r\n                ease: [0.25, 0.46, 0.45, 0.94],\r\n                opacity: { duration: 0.6 },\r\n                filter: { duration: 0.6 },\r\n                scale: { duration: 0.6 },\r\n              }}\r\n              style={{\r\n                top: \"50%\",\r\n                transform: \"translateY(-50%)\",\r\n                textShadow: \"0 0 10px rgba(255, 255, 255, 0.5)\",\r\n              }}\r\n            >\r\n              {line.text}\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;AAJA;;;;;;AAkBO,SAAS,uBAAuB,EACrC,IAAI,EACJ,eAAe,CAAC,EAChB,eAAe,CAAC,EAChB,iBAAiB,IAAI,EACrB,SAAS,EACT,UAAU,EACkB;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,CAAC,MAAc;QACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,GAAG;QAC7D,MAAM,QAAkB,EAAE;QAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,aAAc;YACnD,MAAM,OAAO,MAAM,KAAK,CAAC,GAAG,IAAI,cAAc,IAAI,CAAC;YACnD,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,QAAQ,eAAe,MAAM;IACnC,MAAM,cAAc,KAAK,KAAK,CAAC,eAAe;IAE9C,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,MAAM,MAAM,GAAG,cAAc,GAAG;YAClD,IAAI,gBAAgB,MAAM,MAAM,GAAG,cAAc,KAAK,YAAY;gBAChE;YACF;YACA;QACF;QAEA,MAAM,QAAQ,WAAW;YACvB,gBAAgB,CAAC,OAAS,OAAO;QACnC,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc,MAAM,MAAM;QAAE;QAAa;QAAgB;KAAW;IAExE,MAAM,kBAAkB;QACtB,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,eAAe;QAC9C,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;QAErD,OAAO,MAAM,KAAK,CAAC,YAAY,UAAU,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7D,MAAM;gBACN,eAAe,aAAa;gBAC5B,eAAe;YACjB,CAAC;IACH;IAEA,MAAM,eAAe;IAErB,MAAM,eAAe,CAAC;QACpB,MAAM,qBAAqB,KAAK,GAAG,CAAC,gBAAgB;QAEpD,IAAI,kBAAkB,aAAa;YACjC,8BAA8B;YAC9B,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,GAAG;YACL;QACF,OAAO;YACL,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,IAAI,qBAAqB;YACvD,MAAM,OAAO,qBAAqB;YAClC,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,IAAI,qBAAqB;YAErD,OAAO;gBACL;gBACA,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC;gBACzB;gBACA,GAAG;YACL;QACF;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+FACA;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uEACA,8IAAA,CAAA,UAAS,CAAC,SAAS;wBAErB,SAAS;4BACP,SAAS;4BACT,GAAG;4BACH,QAAQ;4BACR,OAAO;wBACT;wBACA,SAAS;4BACP,GAAG,aAAa,MAAM;4BACtB,GAAG,CAAC,QAAQ,WAAW,IAAI;wBAC7B;wBACA,MAAM;4BACJ,SAAS;4BACT,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO;wBACT;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;gCAAC;gCAAM;gCAAM;gCAAM;6BAAK;4BAC9B,SAAS;gCAAE,UAAU;4BAAI;4BACzB,QAAQ;gCAAE,UAAU;4BAAI;4BACxB,OAAO;gCAAE,UAAU;4BAAI;wBACzB;wBACA,OAAO;4BACL,KAAK;4BACL,WAAW;4BACX,YAAY;wBACd;kCAEC,KAAK,IAAI;uBAlCL,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;AAyCxE", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/ContentDisplayPanel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport { Co<PERSON>, FileText, Code2, Check } from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { a11yDark } from \"react-syntax-highlighter/dist/esm/styles/hljs\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { ScrollingTextAnimation } from \"../shared/ScrollingtextAnimation\";\r\n\r\nconst LetterGlitch = dynamic(() => import(\"../shared/LetterGlitch\"), {\r\n  ssr: false,\r\n});\r\nconst SyntaxHighlighter = dynamic(() => import(\"react-syntax-highlighter\"), {\r\n  ssr: false,\r\n});\r\n\r\nconst sampleText = `\r\nIn the beginning was the Word, and the Word was with God, and the Word was God. \r\nThe same was in the beginning with God. All things were made by him; and without him was not any thing made that was made. \r\nIn him was life; and the life was the light of men. And the light shineth in the darkness; and the darkness comprehended it not. \r\nThere was a man sent from God, whose name was John. The same came for a witness, to bear witness of the Light, that all men through him might believe.\r\n`;\r\n\r\ninterface ScriptItem {\r\n  title: string;\r\n  description: string;\r\n  code?: string;\r\n}\r\n\r\ninterface ContentDisplayPanelProps {\r\n  isGenerating: boolean;\r\n  currentScripts: ScriptItem[];\r\n  manimStreams?: Record<string, string>;\r\n}\r\n\r\nexport default function ContentDisplayPanel({\r\n  isGenerating,\r\n  currentScripts,\r\n}: ContentDisplayPanelProps) {\r\n  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);\r\n  const [showGenerationAnimation, setShowGenerationAnimation] = useState(true);\r\n  const [animationKey, setAnimationKey] = useState(0);\r\n\r\n  // Reset animation when scripts change to refresh the text\r\n  useEffect(() => {\r\n    if (currentScripts.length > 0) {\r\n      setAnimationKey((prev) => prev + 1);\r\n    }\r\n  }, [currentScripts.length]);\r\n\r\n  // Reset animation state when generation starts\r\n  useEffect(() => {\r\n    if (isGenerating) {\r\n      setShowGenerationAnimation(true);\r\n    }\r\n  }, [isGenerating]);\r\n\r\n  const copyToClipboard = async (text: string, index: number) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      setCopiedIndex(index);\r\n      setTimeout(() => setCopiedIndex(null), 2000);\r\n    } catch (err) {\r\n      console.error(\"Failed to copy text: \", err);\r\n    }\r\n  };\r\n\r\n  // Generate dynamic text from current scripts\r\n  const generateDynamicText = () => {\r\n    if (currentScripts.length === 0) {\r\n      return sampleText;\r\n    }\r\n\r\n    return currentScripts\r\n      .map((script, index) => {\r\n        let content = `Scene ${index + 1}: ${script.title}\\n\\n${\r\n          script.description\r\n        }`;\r\n        if (script.code) {\r\n          content += `\\n\\nAnimation Code:\\n${script.code.substring(0, 200)}...`;\r\n        }\r\n        return content;\r\n      })\r\n      .join(\"\\n\\n---\\n\\n\");\r\n  };\r\n\r\n  // Check if generation is complete (has scripts but still generating)\r\n  const isGenerationComplete =\r\n    currentScripts.length > 0 && currentScripts.every((script) => script.code);\r\n\r\n  if (!isGenerating) return null;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"flex flex-col col-span-1 lg:col-span-5 h-full border-l border-dashed bg-background\"\r\n        initial={{ opacity: 0, x: 20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        exit={{ opacity: 0, x: 20 }}\r\n        transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n      >\r\n        <div className=\"px-6 py-4 border-b border-dashed bg-background\">\r\n          <h3 className=\"text-lg\">Generated Content</h3>\r\n        </div>\r\n\r\n        {/* Show generation animation when generating and no complete scripts */}\r\n        {isGenerating && !isGenerationComplete && (\r\n          <div className=\"h-[calc(94vh-var(--header-height))] relative\">\r\n            <LetterGlitch\r\n              glitchColors={[\"#13a564a1\", \"#B75BF6\", \"#2089f9\"]}\r\n              glitchSpeed={40}\r\n              centerVignette={true}\r\n              outerVignette={false}\r\n              smooth={true}\r\n            />\r\n            <ScrollingTextAnimation\r\n              key={animationKey}\r\n              text={generateDynamicText()}\r\n              wordsPerLine={17}\r\n              linesVisible={5}\r\n              animationSpeed={500}\r\n              className=\"rounded-2xl shadow-xl p-8 absolute inset-0\"\r\n              onComplete={() => {\r\n                console.log(\"Animation completed!\");\r\n                if (isGenerationComplete) {\r\n                  setShowGenerationAnimation(false);\r\n                }\r\n              }}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {currentScripts.length > 0 &&\r\n          (!isGenerating ||\r\n            isGenerationComplete ||\r\n            !showGenerationAnimation) && (\r\n            <div className=\"space-y-4 flex-1 px-6 pb-20 mt-4 min-h-0\">\r\n              {currentScripts.map((script, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20, scale: 0.95 }}\r\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                  transition={{ delay: index * 0.1, duration: 0.3 }}\r\n                >\r\n                  <Card className=\"group bg-gradient-to-br from-secondary/5 to-secondary/20 hover:shadow-lg shadow-purple-100 dark:shadow-purple-950/30 duration-300\">\r\n                    <CardHeader className=\"pb-3\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <CardTitle className=\"text-lg font-semibold bg-gradient-to-br from-foreground via-foreground/70 to-secondary bg-clip-text text-transparent\">\r\n                          {script.title}\r\n                        </CardTitle>\r\n                        <Badge\r\n                          variant=\"secondary\"\r\n                          className=\"text-xs px-4 font-medium bg-primary/5 text-primary border-primary/20\"\r\n                        >\r\n                          Scene {index + 1}\r\n                        </Badge>\r\n                      </div>\r\n                    </CardHeader>\r\n                    <CardContent className=\"space-y-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium mb-3 flex items-center text-muted-foreground\">\r\n                          <FileText className=\"w-4 h-4 mr-2 text-primary\" />\r\n                          Description\r\n                        </h4>\r\n                        <div className=\"bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-xl p-4 border border-border/30\">\r\n                          <p className=\"text-sm text-foreground\">\r\n                            {script.description}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      {script.code && (\r\n                        <Accordion type=\"single\" collapsible className=\"w-full\">\r\n                          <AccordionItem value=\"code\">\r\n                            <AccordionTrigger>\r\n                              <div className=\"flex items-center space-x-2 px-4\">\r\n                                <Code2 className=\"w-4 h-4 text-green-400\" />\r\n                                <span>Animation Code</span>\r\n                              </div>\r\n                            </AccordionTrigger>\r\n                            <AccordionContent className=\"px-0 pb-0 rounded-xl overflow-hidden\">\r\n                              <div className=\"border-t border-border/30 \">\r\n                                <div className=\"flex items-center justify-between px-4 py-2 bg-zinc-900/50\">\r\n                                  <span className=\"text-xs text-muted-foreground font-mono\">\r\n                                    Python • Manim\r\n                                  </span>\r\n                                  <Button\r\n                                    size=\"sm\"\r\n                                    variant=\"ghost\"\r\n                                    onClick={() =>\r\n                                      copyToClipboard(script.code!, index)\r\n                                    }\r\n                                    className=\"h-7 px-2 text-xs hover:bg-zinc-800/50\"\r\n                                  >\r\n                                    {copiedIndex === index ? (\r\n                                      <>\r\n                                        <Check className=\"w-3 h-3 mr-1 text-green-400\" />\r\n                                        Copied\r\n                                      </>\r\n                                    ) : (\r\n                                      <>\r\n                                        <Copy className=\"w-3 h-3 mr-1\" />\r\n                                        Copy\r\n                                      </>\r\n                                    )}\r\n                                  </Button>\r\n                                </div>\r\n                                <ScrollArea className=\"h-96 border border-t-0 rounded-b-xl overflow-hidden\">\r\n                                  <SyntaxHighlighter\r\n                                    language=\"python\"\r\n                                    customStyle={{\r\n                                      background: \"transparent\",\r\n                                    }}\r\n                                    style={a11yDark}\r\n                                  >\r\n                                    {script.code}\r\n                                  </SyntaxHighlighter>\r\n                                </ScrollArea>\r\n                              </div>\r\n                            </AccordionContent>\r\n                          </AccordionItem>\r\n                        </Accordion>\r\n                      )}\r\n                    </CardContent>\r\n                  </Card>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;AAmBA,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACzB,KAAK;;AAEP,MAAM,oBAAoB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAC9B,KAAK;;AAGP,MAAM,aAAa,CAAC;;;;;AAKpB,CAAC;AAcc,SAAS,oBAAoB,EAC1C,YAAY,EACZ,cAAc,EACW;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,gBAAgB,CAAC,OAAS,OAAO;QACnC;IACF,GAAG;QAAC,eAAe,MAAM;KAAC;IAE1B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,2BAA2B;QAC7B;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,6CAA6C;IAC7C,MAAM,sBAAsB;QAC1B,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO;QACT;QAEA,OAAO,eACJ,GAAG,CAAC,CAAC,QAAQ;YACZ,IAAI,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EACpD,OAAO,WAAW,EAClB;YACF,IAAI,OAAO,IAAI,EAAE;gBACf,WAAW,CAAC,qBAAqB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;YACvE;YACA,OAAO;QACT,GACC,IAAI,CAAC;IACV;IAEA,qEAAqE;IACrE,MAAM,uBACJ,eAAe,MAAM,GAAG,KAAK,eAAe,KAAK,CAAC,CAAC,SAAW,OAAO,IAAI;IAE3E,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;;8BAE7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAU;;;;;;;;;;;gBAIzB,gBAAgB,CAAC,sCAChB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,cAAc;gCAAC;gCAAa;gCAAW;6BAAU;4BACjD,aAAa;4BACb,gBAAgB;4BAChB,eAAe;4BACf,QAAQ;;;;;;sCAEV,8OAAC,sJAAA,CAAA,yBAAsB;4BAErB,MAAM;4BACN,cAAc;4BACd,cAAc;4BACd,gBAAgB;4BAChB,WAAU;4BACV,YAAY;gCACV,QAAQ,GAAG,CAAC;gCACZ,IAAI,sBAAsB;oCACxB,2BAA2B;gCAC7B;4BACF;2BAXK;;;;;;;;;;;gBAgBV,eAAe,MAAM,GAAG,KACvB,CAAC,CAAC,gBACA,wBACA,CAAC,uBAAuB,mBACxB,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAK;4BAC1C,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,YAAY;gCAAE,OAAO,QAAQ;gCAAK,UAAU;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,OAAO,KAAK;;;;;;8DAEf,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;;wDACX;wDACQ,QAAQ;;;;;;;;;;;;;;;;;;kDAIrB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAA8B;;;;;;;kEAGpD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;4CAIxB,OAAO,IAAI,kBACV,8OAAC,qIAAA,CAAA,YAAS;gDAAC,MAAK;gDAAS,WAAW;gDAAC,WAAU;0DAC7C,cAAA,8OAAC,qIAAA,CAAA,gBAAa;oDAAC,OAAM;;sEACnB,8OAAC,qIAAA,CAAA,mBAAgB;sEACf,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;sEAGV,8OAAC,qIAAA,CAAA,mBAAgB;4DAAC,WAAU;sEAC1B,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA0C;;;;;;0FAG1D,8OAAC,kIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAQ;gFACR,SAAS,IACP,gBAAgB,OAAO,IAAI,EAAG;gFAEhC,WAAU;0FAET,gBAAgB,sBACf;;sGACE,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAAgC;;iHAInD;;sGACE,8OAAC,kMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;kFAMzC,8OAAC,0IAAA,CAAA,aAAU;wEAAC,WAAU;kFACpB,cAAA,8OAAC;4EACC,UAAS;4EACT,aAAa;gFACX,YAAY;4EACd;4EACA,OAAO,sOAAA,CAAA,WAAQ;sFAEd,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3EzB;;;;;;;;;;;;;;;;;;;;;AA4FvB", "debugId": null}}, {"offset": {"line": 1710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\r\n  showCloseButton?: boolean\r\n}) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\r\n          >\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/VideoConfirmationDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { CheckCircle2, Clock, Video, ArrowRight, ExternalLink } from \"lucide-react\";\r\nimport { useGeneration } from \"@/contexts/GenerationContext\";\r\n\r\ninterface VideoConfirmationDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onContinueWorking?: () => void;\r\n  videoJobId: string | null;\r\n  queuePosition?: number | null;\r\n}\r\n\r\nexport default function VideoConfirmationDialog({\r\n  isOpen,\r\n  onClose,\r\n  onContinueWorking,\r\n  videoJobId,\r\n  queuePosition,\r\n}: VideoConfirmationDialogProps) {\r\n  const { state } = useGeneration();\r\n\r\n  const handleGoToDashboard = () => {\r\n    onClose();\r\n    window.location.href = \"/dashboard\";\r\n  };\r\n\r\n  const handleGoToLibrary = () => {\r\n    onClose();\r\n    window.location.href = \"/library\";\r\n  };\r\n\r\n  const handleContinueWorking = () => {\r\n    // Use custom handler if provided, otherwise just close\r\n    if (onContinueWorking) {\r\n      onContinueWorking();\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Check if video is completed\r\n  const isCompleted = state.isCompleted && state.completedVideoUrl;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent>\r\n        <DialogHeader className=\"text-center space-y-4\">\r\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-500/10 rounded-full flex items-center justify-center\">\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n            >\r\n              <CheckCircle2 className=\"w-8 h-8 text-green-500\" />\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.3 }}\r\n          >\r\n            <DialogTitle>\r\n              {isCompleted ? \"Video Generation Complete!\" : \"Video Generation Started!\"}\r\n            </DialogTitle>\r\n          </motion.div>\r\n        </DialogHeader>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"space-y-4\"\r\n        >\r\n          <DialogDescription className=\"text-muted-foreground leading-relaxed\">\r\n            Your animated educational video is now being generated. This process\r\n            typically takes 3-5 minutes.\r\n          </DialogDescription>\r\n\r\n          {videoJobId && (\r\n            <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-primary/10\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse\"></div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"text-sm font-medium text-foreground\">\r\n                    Job ID: {videoJobId}\r\n                  </div>\r\n                  {queuePosition && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      Queue Position: #{queuePosition}\r\n                    </div>\r\n                  )}\r\n                  <div className=\"text-xs text-muted-foreground\">\r\n                    Track your video generation progress\r\n                  </div>\r\n                </div>\r\n                <Video className=\"w-5 h-5 text-primary\" />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg p-4 border border-blue-500/10\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <Clock className=\"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0\" />\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-sm font-medium text-foreground\">\r\n                  What happens next?\r\n                </p>\r\n                <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                  <li>• Your video will be processed in the background</li>\r\n                  <li>• You'll receive a notification when it's ready</li>\r\n                  <li>• The video will be available in your dashboard</li>\r\n                  <li>• You can download or share it directly</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <DialogFooter className=\"flex-col sm:flex-row gap-2\">\r\n          {isCompleted ? (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Generate Another\r\n              </Button>\r\n              <Button onClick={handleGoToLibrary} className=\"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\">\r\n                <span>View in Library</span>\r\n                <ExternalLink className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleContinueWorking}\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Continue Working\r\n              </Button>\r\n              <Button onClick={handleGoToDashboard}>\r\n                <span>Go to Dashboard</span>\r\n                <ArrowRight className=\"w-4 h-4 ml-2\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;AAuBe,SAAS,wBAAwB,EAC9C,MAAM,EACN,OAAO,EACP,iBAAiB,EACjB,UAAU,EACV,aAAa,EACgB;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAE9B,MAAM,sBAAsB;QAC1B;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,wBAAwB;QAC5B,uDAAuD;QACvD,IAAI,mBAAmB;YACrB;QACF,OAAO;YACL;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,cAAc,MAAM,WAAW,IAAI,MAAM,iBAAiB;IAEhE,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;8BACZ,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;0CAEzD,cAAA,8OAAC,qNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CACT,cAAc,+BAA+B;;;;;;;;;;;;;;;;;8BAKpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;;sCAEV,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAwC;;;;;;wBAKpE,4BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAsC;oDAC1C;;;;;;;4CAEV,+BACC,8OAAC;gDAAI,WAAU;;oDAAgC;oDAC3B;;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAIjD,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAsC;;;;;;0DAGnD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACrB,4BACC;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAmB,WAAU;;kDAC5C,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;qDAI5B;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 2266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/components/mvpblocks/bolt.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { useAuthUser } from \"@/hooks/useAuthUser\";\r\nimport InputSection from \"./InputSection\";\r\nimport TaskProgressSidebar from \"./TaskProgressSidebar\";\r\nimport ContentDisplayPanel from \"./ContentDisplayPanel\";\r\nimport VideoConfirmationDialog from \"./VideoConfirmationDialog\";\r\n\r\nconst BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\r\n\r\ninterface Task {\r\n  id: string;\r\n  name: string;\r\n  status: \"pending\" | \"in-progress\" | \"completed\" | \"failed\";\r\n}\r\n\r\ninterface ScriptItem {\r\n  title: string;\r\n  description: string;\r\n  code?: string;\r\n}\r\n\r\nexport default function Bolt() {\r\n  const { user } = useAuthUser();\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [showGenerationUI, setShowGenerationUI] = useState(false);\r\n  const [tasks, setTasks] = useState<Task[]>([]);\r\n  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);\r\n  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);\r\n  const [videoJobId, setVideoJobId] = useState<string | null>(null);\r\n  const [queuePosition, setQueuePosition] = useState<number | null>(null);\r\n\r\n  // Simple sequential generation function\r\n  const generateVideoSequence = async (prompt: string) => {\r\n    try {\r\n      console.log(\"🚀 Starting video generation sequence for:\", prompt);\r\n      \r\n      // Step 1: Generate Scripts\r\n      console.log(\"📝 Step 1: Generating scripts...\");\r\n      setTasks([\r\n        { id: \"1\", name: \"Analyzing Input\", status: \"in-progress\" },\r\n        { id: \"2\", name: \"Generating Script\", status: \"pending\" },\r\n        { id: \"3\", name: \"Generating Animations\", status: \"pending\" },\r\n      ]);\r\n\r\n      const scriptResponse = await fetch(\"/api/generate-script\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ prompt }),\r\n      });\r\n\r\n      if (!scriptResponse.ok) {\r\n        throw new Error(`Script generation failed: ${scriptResponse.status}`);\r\n      }\r\n\r\n      const scripts: ScriptItem[] = await scriptResponse.json();\r\n      console.log(`✅ Generated ${scripts.length} scripts`);\r\n      setCurrentScripts(scripts);\r\n\r\n      setTasks((prev) => prev.map(task => \r\n        task.id === \"1\" ? { ...task, status: \"completed\" } :\r\n        task.id === \"2\" ? { ...task, status: \"in-progress\" } : task\r\n      ));\r\n\r\n      // Step 2: Generate Quiz\r\n      console.log(\"🧠 Step 2: Generating quiz...\");\r\n      const quizResponse = await fetch(\"/api/ai-quiz\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          title: scripts.map(s => s.title).join(\",\"),\r\n          content: scripts.map(s => s.description).join(\"\\n\"),\r\n          userId: user.id,\r\n        }),\r\n      });\r\n\r\n      if (!quizResponse.ok) {\r\n        throw new Error(`Quiz generation failed: ${quizResponse.status}`);\r\n      }\r\n\r\n      const quizData = await quizResponse.json();\r\n      const quizId = quizData.id;\r\n      console.log(`✅ Quiz generated with ID: ${quizId}`);\r\n\r\n      setTasks((prev) => prev.map(task => \r\n        task.id === \"2\" ? { ...task, status: \"completed\" } :\r\n        task.id === \"3\" ? { ...task, status: \"in-progress\" } : task\r\n      ));\r\n\r\n      // Step 3: Generate Manim Codes\r\n      console.log(\"🎨 Step 3: Generating manim codes...\");\r\n      const scriptsWithCode = [];\r\n\r\n      for (let i = 0; i < scripts.length; i++) {\r\n        const script = scripts[i];\r\n        console.log(`🎨 Generating manim code for: ${script.title} (${i + 1}/${scripts.length})`);\r\n\r\n        const manimResponse = await fetch(\"/api/manim\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({\r\n            schema: { title: script.title, description: script.description },\r\n            mainTheme: prompt,\r\n          }),\r\n        });\r\n\r\n        if (manimResponse.ok) {\r\n          const manimData = await manimResponse.json();\r\n          if (manimData.code) {\r\n            scriptsWithCode.push({ ...script, code: manimData.code });\r\n            console.log(`✅ Manim code generated for: ${script.title}`);\r\n          } else {\r\n            console.warn(`⚠️ No code returned for: ${script.title}`);\r\n          }\r\n        } else {\r\n          console.error(`❌ Manim generation failed for: ${script.title}`);\r\n        }\r\n      }\r\n\r\n      console.log(`✅ Generated manim codes for ${scriptsWithCode.length}/${scripts.length} scripts`);\r\n\r\n      setTasks((prev) => prev.map(task => \r\n        task.id === \"3\" ? { ...task, status: \"completed\" } : task\r\n      ));\r\n\r\n      // Step 4: Start Video Rendering\r\n      if (scriptsWithCode.length > 0 && quizId) {\r\n        console.log(\"🎬 Step 4: Starting video rendering...\");\r\n        await startVideoRendering(scriptsWithCode, quizId, prompt);\r\n      } else {\r\n        throw new Error(`Cannot start rendering: ${scriptsWithCode.length} scripts with code, quiz ID: ${quizId}`);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"❌ Generation sequence failed:\", error);\r\n      setTasks((prev) => prev.map(task => \r\n        task.status === \"in-progress\" ? { \r\n          ...task, \r\n          status: \"failed\", \r\n          name: `${task.name} (Failed)` \r\n        } : task\r\n      ));\r\n      setIsGenerating(false);\r\n    }\r\n  };\r\n\r\n  // Start video rendering with backend\r\n  const startVideoRendering = async (scriptsWithCode: ScriptItem[], quizId: string, prompt: string) => {\r\n    try {\r\n      console.log(\"🎬 Starting video rendering...\");\r\n\r\n      // Mark entry as generating in database\r\n      try {\r\n        await fetch(\"/api/start-generation\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ entryId: quizId }),\r\n        });\r\n        console.log(\"✅ Entry marked as generating in database\");\r\n      } catch (dbError) {\r\n        console.error(\"⚠️ Failed to mark entry as generating:\", dbError);\r\n      }\r\n\r\n      // Prepare batch render payload\r\n      const renderPayload = {\r\n        topicName: prompt,\r\n        entryId: quizId,\r\n        scripts: scriptsWithCode.map(script => ({\r\n          manim_code: script.code!,\r\n          description: script.description,\r\n        })),\r\n        priority: 0,\r\n      };\r\n\r\n      console.log(\"📤 Sending batch render request:\", renderPayload);\r\n\r\n      const response = await fetch(`${BACKEND_URL}/batch_render`, {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify(renderPayload),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        throw new Error(`Backend error: ${response.status} - ${errorText}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      console.log(\"✅ Video job queued successfully:\", result);\r\n\r\n      setVideoJobId(result.job_id);\r\n      setQueuePosition(result.queue_position);\r\n\r\n      setTasks((prev) => [...prev, {\r\n        id: \"4\",\r\n        name: \"Rendering Video\",\r\n        status: \"completed\",\r\n      }]);\r\n\r\n      setIsGenerating(false);\r\n      setShowConfirmationDialog(true);\r\n      console.log(\"🎉 Video generation sequence completed!\");\r\n\r\n    } catch (error) {\r\n      console.error(\"❌ Video rendering failed:\", error);\r\n      setTasks((prev) => [...prev, {\r\n        id: \"4\",\r\n        name: `Video Rendering Failed: ${error instanceof Error ? error.message : String(error)}`,\r\n        status: \"failed\",\r\n      }]);\r\n      setIsGenerating(false);\r\n    }\r\n  };\r\n\r\n  // Simple function to handle generation click\r\n  const handleGenerateClick = async (prompt: string) => {\r\n    // Check if user already has an active generation\r\n    try {\r\n      const response = await fetch(\"/api/check-generation-status\");\r\n      if (response.ok) {\r\n        const { hasActiveGeneration, activeEntry } = await response.json();\r\n        \r\n        if (hasActiveGeneration) {\r\n          alert(`You already have a video generation in progress for: \"${activeEntry.prompt}\". Please wait for it to complete before starting a new one.`);\r\n          return;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to check generation status:\", error);\r\n    }\r\n\r\n    setIsGenerating(true);\r\n    setShowGenerationUI(true);\r\n    setCurrentScripts([]);\r\n\r\n    if (typeof window !== \"undefined\") {\r\n      localStorage.setItem(\"currentPrompt\", prompt);\r\n    }\r\n\r\n    await generateVideoSequence(prompt);\r\n  };\r\n\r\n  const handleDialogClose = () => {\r\n    setShowConfirmationDialog(false);\r\n  };\r\n\r\n  const handleResetToInput = () => {\r\n    setShowConfirmationDialog(false);\r\n    setShowGenerationUI(false);\r\n    setIsGenerating(false);\r\n    setCurrentScripts([]);\r\n    setTasks([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <InputSection\r\n        key={\"input-section\"}\r\n        isGenerating={isGenerating}\r\n        onGenerate={handleGenerateClick}\r\n      />\r\n      {showGenerationUI && (\r\n        <div className=\"flex-1 w-full h-0 overflow-hidden\">\r\n          <div className=\"h-full w-full grid grid-cols-1 lg:grid-cols-7 gap-0\">\r\n            <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />\r\n            <ContentDisplayPanel\r\n              isGenerating={isGenerating}\r\n              currentScripts={currentScripts}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <VideoConfirmationDialog\r\n        key={\"video-confirmation-dialog\"}\r\n        isOpen={showConfirmationDialog}\r\n        onClose={handleDialogClose}\r\n        onContinueWorking={handleResetToInput}\r\n        videoJobId={videoJobId}\r\n        queuePosition={queuePosition}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,cAAc,4FAAuC;AAc5C,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,wCAAwC;IACxC,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,QAAQ,GAAG,CAAC,8CAA8C;YAE1D,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,SAAS;gBACP;oBAAE,IAAI;oBAAK,MAAM;oBAAmB,QAAQ;gBAAc;gBAC1D;oBAAE,IAAI;oBAAK,MAAM;oBAAqB,QAAQ;gBAAU;gBACxD;oBAAE,IAAI;oBAAK,MAAM;oBAAyB,QAAQ;gBAAU;aAC7D;YAED,MAAM,iBAAiB,MAAM,MAAM,wBAAwB;gBACzD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,eAAe,MAAM,EAAE;YACtE;YAEA,MAAM,UAAwB,MAAM,eAAe,IAAI;YACvD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YACnD,kBAAkB;YAElB,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,CAAA,OAC1B,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IACjD,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAc,IAAI;YAGzD,wBAAwB;YACxB,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM,MAAM,gBAAgB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,EAAE,IAAI,CAAC;oBACtC,SAAS,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,IAAI,CAAC;oBAC9C,QAAQ,KAAK,EAAE;gBACjB;YACF;YAEA,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,aAAa,MAAM,EAAE;YAClE;YAEA,MAAM,WAAW,MAAM,aAAa,IAAI;YACxC,MAAM,SAAS,SAAS,EAAE;YAC1B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ;YAEjD,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,CAAA,OAC1B,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IACjD,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAc,IAAI;YAGzD,+BAA+B;YAC/B,QAAQ,GAAG,CAAC;YACZ,MAAM,kBAAkB,EAAE;YAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,MAAM,SAAS,OAAO,CAAC,EAAE;gBACzB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;gBAExF,MAAM,gBAAgB,MAAM,MAAM,cAAc;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;4BAAE,OAAO,OAAO,KAAK;4BAAE,aAAa,OAAO,WAAW;wBAAC;wBAC/D,WAAW;oBACb;gBACF;gBAEA,IAAI,cAAc,EAAE,EAAE;oBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;oBAC1C,IAAI,UAAU,IAAI,EAAE;wBAClB,gBAAgB,IAAI,CAAC;4BAAE,GAAG,MAAM;4BAAE,MAAM,UAAU,IAAI;wBAAC;wBACvD,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,OAAO,KAAK,EAAE;oBAC3D,OAAO;wBACL,QAAQ,IAAI,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,EAAE;oBACzD;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,OAAO,KAAK,EAAE;gBAChE;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YAE7F,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,CAAA,OAC1B,KAAK,EAAE,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,IAAI;YAGvD,gCAAgC;YAChC,IAAI,gBAAgB,MAAM,GAAG,KAAK,QAAQ;gBACxC,QAAQ,GAAG,CAAC;gBACZ,MAAM,oBAAoB,iBAAiB,QAAQ;YACrD,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,gBAAgB,MAAM,CAAC,6BAA6B,EAAE,QAAQ;YAC3G;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,CAAC,OAAS,KAAK,GAAG,CAAC,CAAA,OAC1B,KAAK,MAAM,KAAK,gBAAgB;wBAC9B,GAAG,IAAI;wBACP,QAAQ;wBACR,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;oBAC/B,IAAI;YAEN,gBAAgB;QAClB;IACF;IAEA,qCAAqC;IACrC,MAAM,sBAAsB,OAAO,iBAA+B,QAAgB;QAChF,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,uCAAuC;YACvC,IAAI;gBACF,MAAM,MAAM,yBAAyB;oBACnC,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,SAAS;oBAAO;gBACzC;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,SAAS;gBAChB,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;YAEA,+BAA+B;YAC/B,MAAM,gBAAgB;gBACpB,WAAW;gBACX,SAAS;gBACT,SAAS,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC;wBACtC,YAAY,OAAO,IAAI;wBACvB,aAAa,OAAO,WAAW;oBACjC,CAAC;gBACD,UAAU;YACZ;YAEA,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,aAAa,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YACpE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,cAAc,OAAO,MAAM;YAC3B,iBAAiB,OAAO,cAAc;YAEtC,SAAS,CAAC,OAAS;uBAAI;oBAAM;wBAC3B,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;iBAAE;YAEF,gBAAgB;YAChB,0BAA0B;YAC1B,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,CAAC,OAAS;uBAAI;oBAAM;wBAC3B,IAAI;wBACJ,MAAM,CAAC,wBAAwB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;wBACzF,QAAQ;oBACV;iBAAE;YACF,gBAAgB;QAClB;IACF;IAEA,6CAA6C;IAC7C,MAAM,sBAAsB,OAAO;QACjC,iDAAiD;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAAG,MAAM,SAAS,IAAI;gBAEhE,IAAI,qBAAqB;oBACvB,MAAM,CAAC,sDAAsD,EAAE,YAAY,MAAM,CAAC,4DAA4D,CAAC;oBAC/I;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,gBAAgB;QAChB,oBAAoB;QACpB,kBAAkB,EAAE;QAEpB,uCAAmC;;QAEnC;QAEA,MAAM,sBAAsB;IAC9B;IAEA,MAAM,oBAAoB;QACxB,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB;QACzB,0BAA0B;QAC1B,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB,EAAE;QACpB,SAAS,EAAE;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,UAAY;gBAEX,cAAc;gBACd,YAAY;eAFP;;;;;YAIN,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sJAAA,CAAA,UAAmB;4BAAC,cAAc;4BAAc,OAAO;;;;;;sCACxD,8OAAC,sJAAA,CAAA,UAAmB;4BAClB,cAAc;4BACd,gBAAgB;;;;;;;;;;;;;;;;;0BAMxB,8OAAC,0JAAA,CAAA,UAAuB;gBAEtB,QAAQ;gBACR,SAAS;gBACT,mBAAmB;gBACnB,YAAY;gBACZ,eAAe;eALV;;;;;;;;;;;AASb", "debugId": null}}]}